# Requirement: Support for Project Type 2 (Import Test Cases & Generate Requirements)

## Overview
We need to enhance the project creation workflow by introducing a new step and supporting a second project type.  
Currently, we only have **Project Type 1** (generate test cases from requirements).  
We are introducing **Project Type 2**, where users already have existing test cases in a spreadsheet (Excel/CSV) and want to import them, auto-generate requirements from those test cases, and then proceed with test script generation.

---

## Changes in Workflow
### Current Workflow (Project Type 1)
1. Create Project  
2. Define Requirements  
3. Generate Test Cases using LLM from Requirements  
4. Review & Refine Requirements and Test Cases  
5. Proceed to Test Script Generation  

### Updated Workflow  
We will add:
- A **new step after project creation** asking if the user:
  - Wants to generate test cases from requirements (**Project Type 1**), OR  
  - Already has test cases and wants to import them (**Project Type 2**).

#### Project Type 2 Flow:
1. User selects **Project Type 2** during project creation.
2. If no test cases are imported yet:
   - Show **file upload UI** (Excel or CSV).
3. After upload:
   - Parse the file.
   - Display available **sheets** (consider each sheet as a module).
   - User selects which sheets to import/exclude.
4. **Mapping Step**:
   - Ask user:
     - Starting row for test cases (to skip summary rows).
     - Mapping of columns:
       - Submodule Name  
       - Test Case Title  (Optional, if not given we need to generate it from steps through LLM along with generating the requirement)
       - Test Case Steps  
       - Expected Result  
       - Additional Comments/Notes  (Optional)
5. Import data:
   - For each sheet (module):
     - Parse submodules and their test cases.
     - Auto-generate **Requirement names**:  
       Format: `<SheetName>_<SubmoduleName>` (e.g., `Sheet1_Login`).
     - For each requirement:
       - Send its test cases to LLM to **generate a Requirement Description**.
6. Save:
   - Imported test cases.
   - Generated requirements (with titles auto-derived from sheet/submodule).
7. After import:
   - User sees requirements & test cases in the standard view (same as Project Type 1).
   - User can refine requirements (via "Refine Requirement" button).
8. Proceed to variable setup and test script generation (same as Project Type 1).

---

## Key Functional Points
1. **New Project Creation Step**:
   - Add a radio button to select project type:
     - **Type 1:** Generate test cases from requirement (existing flow).
     - **Type 2:** Import test cases and auto-generate requirements.

2. **File Upload Handling**:
   - Support Excel (XLSX).
   - Handle multiple sheets in Excel.
   - Treat each sheet as a **module**.
   - Each sheet can have **submodules**, determined by column mapping.

3. **Mapping UI**:
   - After parsing uploaded file:
     - Display preview of file content.
     - User maps columns to our test case structure:
       - Test Case Title
       - Test Case Steps
       - Expected Result
       - Notes/Comments
       - (Optional) Title Column
       - Submodule Column  
     - User sets starting row for test case data.

4. **Requirement & Test Case Import**:
   - For each sheet → generate modules.
   - For each submodule → generate requirement name and import its test cases.
   - Send test cases to LLM to auto-generate requirement description.

5. **Data Model Updates**:
   - Introduce `project_type` field (1 or 2) in Project entity.
   - For imported projects (type 2):
     - Requirements and test case title (if not provided, since it is optional) are auto-generated from sheets/submodules.
     - Test cases are imported directly (no LLM test case generation).

6. **LLM Integration for Requirement Description**:
   - Given a list of test cases under a requirement:
     - Generate a concise requirement description.
   - Store it as requirement description.

7. **Post-Import Behavior**:
   - Same as current flow:
     - Users can view/edit requirements and test cases.
     - Users can refine requirements using LLM.
     - Proceed to variable configuration and test script generation.

---

## Example Use Case

**Excel File (uploaded by user):**
- Sheet 1: `Login`
  - Submodules:  
    - `Login Functionality` (5 test cases)  
    - `Forgot Password` (5 test cases)  
    - `Registration` (8 test cases)

**Result after Import:**
- Requirements:
  - `Sheet1_Login`
  - `Sheet1_ForgotPassword`
  - `Sheet1_Registration`
- Each requirement contains its imported test cases.
- LLM generates descriptions for each requirement.

---

## Acceptance Criteria
- [ ] Add "Project Type" selection during project creation.
- [ ] Implement file upload (Excel/CSV) with robust parsing.
- [ ] Build sheet & column mapping UI.
- [ ] Parse sheets → modules → submodules → test cases.
- [ ] Auto-generate requirement names from sheets/submodules.
- [ ] Use LLM to generate requirement descriptions from imported test cases.
- [ ] Save imported test cases and requirements in system.
- [ ] Post-import, maintain same flow as Project Type 1 (refine requirements, add variables, generate test scripts).

---

## Notes
- **Backward Compatibility:** Existing projects remain Project Type 1 by default.
- **Mapping Reusability:** If multiple sheets are selected, the mapping applies uniformly across them.
- **Test Case ID:** System-generated (e.g., TC-001, TC-002).
- **Error Handling:**
  - Invalid/malformed files should prompt a clear error message.
  - Unsupported formats should be rejected gracefully.
- **Notes:**
  - The UI should be elegant and modern and should match the application's design and color palette.
  - Make sure to throughly test each of the project type flow once your implementation is done.
  - The code should be top-notch.
  - make sure you are not introducing any error and build it before saying you are done.
---
