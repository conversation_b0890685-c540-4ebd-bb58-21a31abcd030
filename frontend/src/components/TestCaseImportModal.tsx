'use client';

import { useState } from 'react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogBody, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileSpreadsheet, X } from 'lucide-react';
import { FileUploadComponent } from './FileUploadComponent';
import { SheetSelectionComponent } from './SheetSelectionComponent';
import { ColumnMappingComponent } from './ColumnMappingComponent';
import { ImportProgressComponent } from './ImportProgressComponent';
import { FileUploadResponse, ColumnMapping, ImportResponse } from '@/types';

interface TestCaseImportModalProps {
  projectId: number;
  onClose: () => void;
  onImportComplete: (result: ImportResponse) => void;
}

export function TestCaseImportModal({ projectId, onClose, onImportComplete }: Readonly<TestCaseImportModalProps>) {
  const [step, setStep] = useState(1);
  const [fileData, setFileData] = useState<FileUploadResponse | null>(null);
  const [selectedSheets, setSelectedSheets] = useState<string[]>([]);
  const [columnMapping, setColumnMapping] = useState<ColumnMapping>({
    steps_column: '',
    expected_result_column: '',
    starting_row: 2
  });
  const [error, setError] = useState('');

  const handleFileUploaded = (response: FileUploadResponse) => {
    setFileData(response);
    setError('');
    setStep(2);
  };

  const handleFileUploadError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleSheetsSelected = (sheets: string[]) => {
    setSelectedSheets(sheets);
  };

  const handleNextFromSheets = () => {
    setStep(3);
  };

  const handleBackToSheets = () => {
    setStep(2);
  };

  const handleMappingChange = (mapping: ColumnMapping) => {
    setColumnMapping(mapping);
  };

  const handleNextFromMapping = () => {
    setStep(4);
  };

  const handleBackToMapping = () => {
    setStep(3);
  };

  const handleImportComplete = (result: ImportResponse) => {
    onImportComplete(result);
  };

  const handleBackToUpload = () => {
    setStep(1);
    setFileData(null);
    setSelectedSheets([]);
    setColumnMapping({
      steps_column: '',
      expected_result_column: '',
      starting_row: 2
    });
    setError('');
  };

  const getStepTitle = () => {
    switch (step) {
      case 1: return 'Upload File';
      case 2: return 'Select Sheets';
      case 3: return 'Configure Columns';
      case 4: return 'Import Progress';
      default: return 'Import Test Cases';
    }
  };

  const getStepDescription = () => {
    switch (step) {
      case 1: return 'Upload your Excel file containing test cases';
      case 2: return 'Choose which sheets to import from your file';
      case 3: return 'Map your columns to test case fields';
      case 4: return 'Importing test cases and generating requirements';
      default: return 'Import test cases from spreadsheet';
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="!max-w-[700px] max-h-[92vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileSpreadsheet className="h-6 w-6 text-green-600" />
            <div>
              <DialogTitle>{getStepTitle()}</DialogTitle>
              <DialogDescription>
                Step {step} of 4: {getStepDescription()}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <DialogBody>
          {error && step === 1 && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          {step === 1 && (
            <FileUploadComponent
              onFileUploaded={handleFileUploaded}
              onError={handleFileUploadError}
            />
          )}

          {step === 2 && fileData && (
            <SheetSelectionComponent
              fileData={fileData}
              selectedSheets={selectedSheets}
              onSheetsSelected={handleSheetsSelected}
            />
          )}

          {step === 3 && fileData && (
            <ColumnMappingComponent
              selectedSheets={selectedSheets}
              columnMapping={columnMapping}
              onMappingChange={handleMappingChange}
            />
          )}

          {step === 4 && fileData && (
            <ImportProgressComponent
              projectId={projectId}
              fileId={fileData.file_id}
              selectedSheets={selectedSheets}
              columnMapping={columnMapping}
              onComplete={handleImportComplete}
              onBack={handleBackToMapping}
            />
          )}
        </DialogBody>

        {/* DialogFooter for steps 2, 3, and 4 */}
        {step === 2 && (
          <DialogFooter>
            <Button variant="outline" onClick={handleBackToUpload}>
              Back
            </Button>
            <Button
              onClick={handleNextFromSheets}
              disabled={selectedSheets.length === 0}
            >
              Next: Configure Columns
            </Button>
          </DialogFooter>
        )}

        {step === 3 && (
          <DialogFooter>
            <Button variant="outline" onClick={handleBackToSheets}>
              Back
            </Button>
            <Button
              onClick={handleNextFromMapping}
              disabled={!columnMapping.steps_column}
            >
              Import Test Cases
            </Button>
          </DialogFooter>
        )}

        {step === 4 && (
          <DialogFooter>
            <Button variant="outline" onClick={handleBackToMapping}>
              Import More Files
            </Button>
            <Button onClick={() => window.location.reload()}>
              View Project
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
