'use client';

import { useState, useEffect } from 'react';

import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  TestTube, 
  ChevronDown, 
  ChevronRight,
  Sparkles,
  Download
} from 'lucide-react';
import { ImportResponse, ImportRequest, ColumnMapping } from '@/types';
import { apiClient } from '@/lib/api';

interface ImportProgressComponentProps {
  projectId: number;
  fileId: string;
  selectedSheets: string[];
  columnMapping: ColumnMapping;
  onComplete: (result: ImportResponse) => void;
  onBack: () => void;
}

export function ImportProgressComponent({
  projectId,
  fileId,
  selectedSheets,
  columnMapping,
  onComplete,
  onBack
}: Readonly<ImportProgressComponentProps>) {
  const [importing, setImporting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [result, setResult] = useState<ImportResponse | null>(null);
  const [error, setError] = useState('');
  const [expandedRequirements, setExpandedRequirements] = useState<Set<string>>(new Set());

  useEffect(() => {
    startImport();
  }, []);

  const startImport = async () => {
    setImporting(true);
    setProgress(0);
    setCurrentStep('Preparing import...');

    try {
      // Simulate progress steps
      const steps = [
        { message: 'Parsing file data...', progress: 20 },
        { message: 'Extracting test cases...', progress: 40 },
        { message: 'Generating requirement descriptions...', progress: 60 },
        { message: 'Creating requirements...', progress: 80 },
        { message: 'Finalizing import...', progress: 95 }
      ];

      for (const step of steps) {
        setCurrentStep(step.message);
        setProgress(step.progress);
        await new Promise(resolve => setTimeout(resolve, 800));
      }

      const importRequest: ImportRequest = {
        file_id: fileId,
        selected_sheets: selectedSheets,
        column_mapping: columnMapping
      };

      const response = await apiClient.importTestCases(projectId, importRequest);
      
      setProgress(100);
      setCurrentStep('Import completed successfully!');
      setResult(response);
      onComplete(response);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Import failed');
    } finally {
      setImporting(false);
    }
  };

  const toggleRequirement = (requirementName: string) => {
    setExpandedRequirements(prev => {
      const newSet = new Set(prev);
      if (newSet.has(requirementName)) {
        newSet.delete(requirementName);
      } else {
        newSet.add(requirementName);
      }
      return newSet;
    });
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-6 w-6 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-red-900">Import Failed</h3>
              <p className="text-sm text-red-800 mt-1">{error}</p>
            </div>
          </div>
        </div>


      </div>
    );
  }

  if (importing || !result) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <Sparkles className="h-6 w-6 text-blue-600 absolute top-3 left-3" />
            </div>
          </div>
          <h3 className="text-lg font-medium">Importing Test Cases</h3>
          <p className="text-sm text-gray-600 mt-1">{currentStep}</p>
        </div>

        <div className="space-y-2">
          <Progress value={progress} className="w-full" />
          <p className="text-xs text-gray-500 text-center">{progress}% complete</p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <Sparkles className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-blue-900">AI is working on your requirements</p>
              <p className="text-blue-800 mt-1">
                We&apos;re analyzing your test cases and generating meaningful requirement descriptions using AI.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Success Header */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <CheckCircle className="h-6 w-6 text-green-600 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-green-900">Import Completed Successfully!</h3>
            <p className="text-sm text-green-800 mt-1">{result.message}</p>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{result.requirements.length}</p>
                <p className="text-sm text-gray-600">Requirements Created</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TestTube className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{result.total_test_cases}</p>
                <p className="text-sm text-gray-600">Test Cases Imported</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Download className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{selectedSheets.length}</p>
                <p className="text-sm text-gray-600">Sheets Processed</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Requirements List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Imported Requirements</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {result.requirements.map((requirement, index) => {
            const isExpanded = expandedRequirements.has(requirement.name);
            
            return (
              <Collapsible key={requirement.name}>
                <CollapsibleTrigger
                  onClick={() => toggleRequirement(requirement.name)}
                  className="w-full"
                >
                  <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-3 text-left">
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4 text-gray-500" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-500" />
                      )}
                      <div>
                        <h4 className="font-medium">{requirement.name}</h4>
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {requirement.description}
                        </p>
                      </div>
                    </div>
                    <Badge variant="secondary">
                      {requirement.test_cases.length} test case{requirement.test_cases.length !== 1 ? 's' : ''}
                    </Badge>
                  </div>
                </CollapsibleTrigger>
                
                <CollapsibleContent>
                  <div className="ml-7 mt-2 space-y-2">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm font-medium text-gray-700 mb-2">AI-Generated Description:</p>
                      <p className="text-sm text-gray-800">{requirement.description}</p>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">Test Cases:</p>
                      {requirement.test_cases.map((testCase, tcIndex) => (
                        <div key={tcIndex} className="border-l-2 border-blue-200 pl-3 py-2">
                          <p className="text-sm font-medium">{testCase.title || `Test Case ${tcIndex + 1}`}</p>
                          <p className="text-xs text-gray-600 mt-1">
                            <strong>Steps:</strong> {testCase.steps}
                          </p>
                          {testCase.expected_result && (
                            <p className="text-xs text-gray-600">
                              <strong>Expected:</strong> {testCase.expected_result}
                            </p>
                          )}
                          {testCase.notes && (
                            <p className="text-xs text-gray-600">
                              <strong>Notes:</strong> {testCase.notes}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            );
          })}
        </CardContent>
      </Card>


    </div>
  );
}
