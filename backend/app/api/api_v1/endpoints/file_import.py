from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status
from sqlalchemy.orm import Session
from app.api.deps import get_db, get_current_active_user
from app.crud.project import get_project, is_project_member
from app.crud.requirement import create_requirement as crud_create_requirement, get_requirements_by_project_id, update_requirement
from app.crud.test_case import create_test_case, get_test_cases_for_requirement, delete_test_case
from app.models.user import User
from app.models.project import ProjectType
from app.schemas.project import (
    FileUploadResponse, SheetPreview, ImportRequest, ImportResponse,
    ColumnMapping
)
from app.schemas.requirement import RequirementCreate, RequirementUpdate, TestCaseCreate
from app.services.file_import_service import file_import_service
from app.services.requirement_generation_service import requirement_generation_service
from app.utils.slug import generate_requirement_slug
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Upload Excel file for import."""
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    # Additional file size validation (10MB limit)
    if file.size and file.size > 10 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="File size exceeds 10MB limit")

    return await file_import_service.upload_file(file)

@router.get("/preview/{file_id}/{sheet_name}", response_model=SheetPreview)
def get_sheet_preview(
    file_id: str,
    sheet_name: str,
    preview_rows: int = 5,
    starting_row: int = 1,
    current_user: User = Depends(get_current_active_user)
):
    """Get preview of a specific sheet."""
    return file_import_service.get_sheet_preview(file_id, sheet_name, preview_rows, starting_row)

@router.post("/import/{project_id}", response_model=ImportResponse)
async def import_test_cases(
    project_id: int,
    import_request: ImportRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Import test cases from uploaded file and generate requirements."""
    
    # Check if user has access to the project
    project = get_project(db, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    if not is_project_member(db, project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to import test cases to this project"
        )
    
    # Verify project is Type 2 (import test cases)
    if project.project_type != ProjectType.IMPORT_TEST_CASES:
        raise HTTPException(
            status_code=400,
            detail="This project is not configured for test case import"
        )
    
    try:
        # Get existing requirements for intelligent import
        existing_requirements = get_requirements_by_project_id(db, project_id)
        existing_req_names = [req.name for req in existing_requirements]

        # Parse the file and extract test cases
        imported_requirements = file_import_service.parse_and_import(
            import_request.file_id,
            import_request.selected_sheets,
            import_request.column_mapping,
            existing_req_names
        )
        
        if not imported_requirements:
            raise HTTPException(status_code=400, detail="No valid test cases found in the file")
        
        # Separate new and existing requirements for bulk processing
        new_requirements = []
        existing_requirements_to_update = []
        created_requirements = []
        total_test_cases = 0

        # First pass: generate titles and separate new vs existing requirements
        for imported_req in imported_requirements:
            # Generate simple incremental titles for test cases that don't have them
            for i, test_case in enumerate(imported_req.test_cases, 1):
                if not test_case.title:
                    test_case.title = f"Test Case {i:02d}"

            # Check if requirement already exists
            existing_requirement = None
            for existing_req in existing_requirements:
                if existing_req.name == imported_req.name:
                    existing_requirement = existing_req
                    break

            if existing_requirement:
                existing_requirements_to_update.append((existing_requirement, imported_req))
            else:
                new_requirements.append(imported_req)

        # Process existing requirements (keep individual processing for complexity)
        for existing_requirement, imported_req in existing_requirements_to_update:
            # Update existing requirement - get existing test cases and combine with new ones
            existing_test_cases = get_test_cases_for_requirement(db, existing_requirement.id)

            # Combine existing and new test cases for description generation
            all_test_cases = imported_req.test_cases.copy()

            # Generate updated requirement description from all test cases
            description = await requirement_generation_service.generate_requirement_description(
                imported_req.name, all_test_cases
            )

            # Update requirement description
            requirement_update = RequirementUpdate(description=description)
            update_requirement(db, existing_requirement.id, requirement_update)

            # Add only new test cases (simple approach: add all imported test cases)
            # In a more sophisticated implementation, we could compare test case content
            for i, test_case in enumerate(imported_req.test_cases, len(existing_test_cases) + 1):
                test_case_data = TestCaseCreate(
                    title=test_case.title or f"Test Case {i}",
                    steps=test_case.steps,
                    expected_result=test_case.expected_result or "",
                    notes=test_case.notes,
                    custom_id=None  # Will be auto-generated
                )

                create_test_case(db, test_case_data, existing_requirement.id)
                total_test_cases += 1

            # Update the imported requirement with the generated description
            imported_req.description = description
            # Note: Don't append to created_requirements here - this is for existing requirements

        # Process new requirements individually to ensure proper mapping
        for imported_req in new_requirements:
            # Generate requirement description
            description = await requirement_generation_service.generate_requirement_description(
                imported_req.name, imported_req.test_cases
            )

            # Create requirement in database
            requirement_data = RequirementCreate(
                name=imported_req.name,
                description=description,
                project_id=project_id,
                tag_names=[]
            )

            db_requirement = crud_create_requirement(db, requirement_data, current_user.id)

            # Create test cases for this requirement
            for i, test_case in enumerate(imported_req.test_cases, 1):
                test_case_data = TestCaseCreate(
                    title=test_case.title or f"Test Case {i:02d}",
                    steps=test_case.steps,
                    expected_result=test_case.expected_result or "",
                    notes=test_case.notes,
                    custom_id=None  # Will be auto-generated
                )

                create_test_case(db, test_case_data, db_requirement.id)
                total_test_cases += 1

            # Update the imported requirement with the generated description
            imported_req.description = description
            created_requirements.append(imported_req)
        
        # Clean up the uploaded file
        file_import_service.cleanup_file(import_request.file_id)
        
        return ImportResponse(
            requirements=created_requirements,
            total_test_cases=total_test_cases,
            message=f"Successfully imported {len(created_requirements)} requirements with {total_test_cases} test cases"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        # Clean up the uploaded file on error
        file_import_service.cleanup_file(import_request.file_id)
        raise HTTPException(status_code=500, detail=f"Import failed: {str(e)}")

@router.delete("/cleanup/{file_id}")
def cleanup_file(
    file_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Clean up uploaded file."""
    file_import_service.cleanup_file(file_id)
    return {"message": "File cleaned up successfully"}

@router.get("/validate-mapping/{file_id}")
def validate_column_mapping(
    file_id: str,
    sheet_name: str,
    column_mapping: ColumnMapping,
    current_user: User = Depends(get_current_active_user)
):
    """Validate column mapping against the file structure."""
    try:
        # Get sheet preview to validate columns exist
        preview = file_import_service.get_sheet_preview(file_id, sheet_name, 1)
        headers = preview.headers
        
        errors = []
        
        # Check required columns
        if column_mapping.steps_column not in headers:
            errors.append(f"Steps column '{column_mapping.steps_column}' not found in sheet")
        
        if column_mapping.expected_result_column not in headers:
            errors.append(f"Expected result column '{column_mapping.expected_result_column}' not found in sheet")
        
        # Check optional columns if specified
        if column_mapping.submodule_column and column_mapping.submodule_column not in headers:
            errors.append(f"Submodule column '{column_mapping.submodule_column}' not found in sheet")
        
        if column_mapping.title_column and column_mapping.title_column not in headers:
            errors.append(f"Title column '{column_mapping.title_column}' not found in sheet")
        
        if column_mapping.notes_column and column_mapping.notes_column not in headers:
            errors.append(f"Notes column '{column_mapping.notes_column}' not found in sheet")
        
        # Check starting row
        if column_mapping.starting_row < 1 or column_mapping.starting_row > preview.total_rows:
            errors.append(f"Starting row {column_mapping.starting_row} is out of range (1-{preview.total_rows})")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "available_columns": headers
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Validation failed: {str(e)}")
