from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime
from app.models.project import AutomationFramework, ProgrammingLanguage, ProjectType
from app.models.project_member import MemberRole

class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    code_source: Optional[str] = None
    automation_framework: Optional[AutomationFramework] = None
    programming_language: Optional[ProgrammingLanguage] = None
    project_type: ProjectType = ProjectType.GENERATE_FROM_REQUIREMENTS

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    code_source: Optional[str] = None
    automation_framework: Optional[AutomationFramework] = None
    programming_language: Optional[ProgrammingLanguage] = None
    project_type: Optional[ProjectType] = None

class ProjectMemberBase(BaseModel):
    user_id: int
    role: MemberRole = MemberRole.MEMBER

class ProjectMemberCreate(ProjectMemberBase):
    pass

class ProjectMember(ProjectMemberBase):
    id: int
    project_id: int
    user_email: str
    user_name: Optional[str] = None
    joined_at: datetime

    class Config:
        from_attributes = True

class ProjectInDBBase(ProjectBase):
    id: int
    slug: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Project(ProjectInDBBase):
    members: List[ProjectMember] = []

class ProjectInDB(ProjectInDBBase):
    pass

# File Import Schemas for Project Type 2
class FileUploadResponse(BaseModel):
    file_id: str
    filename: str
    sheets: List[str]
    total_rows: int

class SheetPreview(BaseModel):
    sheet_name: str
    headers: List[str]
    sample_rows: List[List[str]]
    total_rows: int

class ColumnMapping(BaseModel):
    submodule_column: Optional[str] = None
    title_column: Optional[str] = None  # Optional
    steps_column: str
    expected_result_column: Optional[str] = None  # Optional
    notes_column: Optional[str] = None  # Optional
    starting_row: int = 2  # Default to row 2 (skip header)

class ImportRequest(BaseModel):
    file_id: str
    selected_sheets: List[str]
    column_mapping: ColumnMapping

class ImportedTestCase(BaseModel):
    title: Optional[str] = None
    steps: str
    expected_result: Optional[str] = None
    notes: Optional[str] = None

class ImportedRequirement(BaseModel):
    name: str
    description: str
    test_cases: List[ImportedTestCase]

class ImportResponse(BaseModel):
    requirements: List[ImportedRequirement]
    total_test_cases: int
    message: str

class ProjectListItem(BaseModel):
    id: int
    name: str
    slug: str
    description: Optional[str] = None
    created_at: datetime
    member_count: int
    requirement_count: int
    test_case_count: int
    user_role: MemberRole

    class Config:
        from_attributes = True
