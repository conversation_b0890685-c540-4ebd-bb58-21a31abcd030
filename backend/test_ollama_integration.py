#!/usr/bin/env python3
"""
Test Ollama integration for requirement generation
"""
import asyncio
from app.services.requirement_generation_service import requirement_generation_service
from app.schemas.project import ImportedTestCase

async def test_requirement_generation():
    """Test requirement generation with Ollama"""
    print("🧪 Testing Ollama Integration for Requirement Generation")
    print("=" * 60)
    
    # Sample test cases
    test_cases = [
        ImportedTestCase(
            title="Valid Login Test",
            steps="Enter valid username 'testuser' and password 'password123', click Login button",
            expected_result="User should be redirected to dashboard with welcome message",
            notes="Test with different browsers"
        ),
        ImportedTestCase(
            title="Invalid Login Test", 
            steps="Enter invalid username 'wronguser' and valid password 'password123', click Login button",
            expected_result="Error message 'Invalid username or password' should be displayed",
            notes="Check error message styling"
        )
    ]
    
    try:
        print("📝 Generating requirement description...")
        description = await requirement_generation_service.generate_requirement_description(
            "User Authentication", 
            test_cases
        )
        
        print("✅ Generated Requirement Description:")
        print("-" * 40)
        print(description)
        print("-" * 40)
        
        # Test title generation
        print("\n🏷️  Testing test case title generation...")
        title = await requirement_generation_service.generate_test_case_title(
            "Click forgot password link, enter email, submit form",
            "Password reset email should be sent to user"
        )
        
        print(f"✅ Generated Test Case Title: '{title}'")
        
        print("\n🎉 Ollama integration test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error testing Ollama integration: {e}")
        print("\n💡 Troubleshooting:")
        print("1. Make sure Ollama is running: ollama serve")
        print("2. Check if model is available: ollama list")
        print("3. Pull model if needed: ollama pull llama3.2")

async def test_fallback_generation():
    """Test fallback generation when Ollama is not available"""
    print("\n🔄 Testing Fallback Generation")
    print("=" * 40)
    
    test_cases = [
        ImportedTestCase(
            title="Search Test",
            steps="Enter search term and click search button",
            expected_result="Search results should be displayed",
            notes=""
        )
    ]
    
    # Test fallback description
    fallback_desc = requirement_generation_service._generate_fallback_description(
        "Search Functionality", 
        test_cases
    )
    
    print("✅ Fallback Description:")
    print(fallback_desc)
    
    # Test fallback title
    fallback_title = requirement_generation_service._generate_fallback_title(
        "Enter search term and click search",
        "Results should be displayed"
    )
    
    print(f"✅ Fallback Title: '{fallback_title}'")

if __name__ == "__main__":
    asyncio.run(test_requirement_generation())
    asyncio.run(test_fallback_generation())
