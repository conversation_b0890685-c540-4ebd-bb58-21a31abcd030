#!/usr/bin/env python3
"""
Test script to verify file upload endpoint works
"""
import requests
import tempfile
import pandas as pd
import os

def create_test_excel():
    """Create a test Excel file"""
    data = {
        'Submodule': ['Login', 'Login', 'Registration'],
        'Test Title': ['Valid Login', 'Invalid Login', 'Valid Registration'],
        'Test Steps': [
            'Enter valid username and password, click login',
            'Enter invalid username, click login', 
            'Fill registration form with valid data, submit'
        ],
        'Expected Result': [
            'User should be logged in successfully',
            'Error message should be displayed',
            'Account should be created successfully'
        ],
        'Notes': ['', 'Check error message text', '']
    }
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        df = pd.DataFrame(data)
        df.to_excel(tmp_file.name, index=False)
        return tmp_file.name

def test_upload_endpoint():
    """Test the upload endpoint"""
    print("Testing file upload endpoint...")
    
    # Create test file
    test_file_path = create_test_excel()
    print(f"Created test file: {test_file_path}")
    
    try:
        # Test upload without authentication (should fail with 401)
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post('http://localhost:8000/api/v1/file-import/upload', files=files)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response content: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("✓ Endpoint is accessible but requires authentication (expected)")
        elif response.status_code == 200:
            print("✓ Upload successful!")
            result = response.json()
            print(f"File ID: {result.get('file_id')}")
            print(f"Sheets: {result.get('sheets')}")
        else:
            print(f"✗ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"✗ Error testing upload: {e}")
    finally:
        # Clean up
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)

if __name__ == "__main__":
    test_upload_endpoint()
